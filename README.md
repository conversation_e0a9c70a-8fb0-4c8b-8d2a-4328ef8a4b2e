# 网页信息显示器 - 浏览器扩展

基于 Plasmo 框架开发的浏览器扩展，用于显示当前网页的标题、地址等信息，支持多种显示模式。

## 🚀 重构完成

项目已按照模块化思想重新拆分，修复了网站标题、地址等信息无法正常显示的BUG。

### ✨ 重构亮点

- **模块化架构**: 按功能拆分为独立模块，提高代码可维护性
- **类型安全**: 完整的TypeScript类型定义
- **组件复用**: 可复用的UI组件库
- **服务分离**: 独立的服务层处理业务逻辑
- **错误处理**: 完善的错误处理和降级方案

## 功能特性

### 🎯 核心功能
- ✅ 显示当前网页标题
- ✅ 显示当前网页完整地址
- ✅ 显示网页域名和协议
- ✅ 一键复制页面信息到剪贴板
- ✅ 自动保存访问历史记录

### 📱 多种显示模式

#### 1. 弹窗模式 (Popup) ✅
- 点击扩展图标打开弹窗
- 紧凑的信息显示界面
- 快速复制功能
- 实时更新页面信息

#### 2. 悬浮窗模式 (Floating Window) ✅
- 可拖动的悬浮窗口
- 使用 Shadow DOM 技术，不影响原网页
- 快捷键 `Ctrl+Shift+I` 切换显示/隐藏
- 右键菜单快速调用
- 可最小化为小按钮
- 实时更新页面信息

#### 3. 侧边栏模式 (Side Panel) ✅
- 完整的侧边栏界面
- 显示当前页面详细信息
- 最近访问历史记录（最多50条）
- 自动更新页面信息
- 点击历史记录在新标签页打开

## 安装和使用

### 开发环境设置

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

3. 构建生产版本：
```bash
npm run build
```

4. 打包扩展：
```bash
npm run package
```

### 浏览器安装

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的 `build` 文件夹

## 使用方法

### 弹窗模式
1. 点击浏览器工具栏中的扩展图标
2. 在弹出的窗口中查看页面信息
3. 点击"复制"按钮复制相应信息

### 悬浮窗模式
1. 使用快捷键 `Ctrl+Shift+I` 打开/关闭悬浮窗
2. 或者右键点击页面，选择"显示页面信息"
3. 拖动标题栏移动悬浮窗位置
4. 点击 × 按钮关闭悬浮窗

### 侧边栏模式
1. 在扩展管理页面启用侧边栏功能
2. 点击浏览器侧边栏中的扩展图标
3. 查看当前页面信息和访问历史
4. 点击历史记录项在新标签页中打开

## 技术特性

- **框架**: Plasmo - 现代化的浏览器扩展开发框架
- **语言**: TypeScript + React
- **样式**: 内联样式，确保兼容性
- **架构**: 
  - Content Scripts: 页面内容注入
  - Background Scripts: 后台服务
  - Popup: 弹窗界面
  - Side Panel: 侧边栏界面

## 权限说明

扩展需要以下权限：
- `activeTab`: 获取当前活动标签页信息
- `tabs`: 访问标签页标题和URL
- `contextMenus`: 创建右键菜单
- `host_permissions`: 在所有网站上运行

## 📁 项目结构

### 重构后的模块化结构

```
├── src/                    # 源代码目录
│   ├── shared/            # 共享模块
│   │   ├── types.ts       # 类型定义
│   │   ├── utils.ts       # 工具函数
│   │   └── constants.ts   # 常量定义
│   ├── services/          # 服务层
│   │   ├── pageInfoService.ts    # 页面信息服务
│   │   ├── storageService.ts     # 存储服务
│   │   └── backgroundService.ts  # 后台服务
│   ├── components/        # UI组件
│   │   ├── Button.tsx     # 按钮组件
│   │   ├── PageInfoDisplay.tsx   # 页面信息显示组件
│   │   ├── HistoryList.tsx       # 历史记录列表组件
│   │   └── index.ts       # 组件导出
│   ├── pages/             # 页面组件
│   │   ├── popup.tsx      # 弹窗页面
│   │   ├── sidepanel.tsx  # 侧边栏页面
│   │   └── floating-window.tsx   # 悬浮窗页面
│   └── index.ts           # 主导出文件
├── popup.tsx              # 弹窗入口文件
├── sidepanel.tsx          # 侧边栏入口文件
├── contents/
│   └── floating-window.tsx # 悬浮窗内容脚本
├── background.ts          # 后台脚本入口
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript配置
├── README.md             # 项目说明
└── USAGE.md              # 使用说明
```

### 模块说明

#### 🔧 共享模块 (src/shared/)
- **types.ts**: 定义所有TypeScript类型接口
- **utils.ts**: 通用工具函数（复制、时间格式化、防抖等）
- **constants.ts**: 应用常量（样式、尺寸、错误消息等）

#### 🛠️ 服务层 (src/services/)
- **pageInfoService.ts**: 页面信息获取和监听服务
- **storageService.ts**: 数据存储和历史记录管理服务
- **backgroundService.ts**: 后台脚本业务逻辑服务

#### 🎨 组件层 (src/components/)
- **Button.tsx**: 可复用的按钮组件
- **PageInfoDisplay.tsx**: 页面信息显示组件
- **HistoryList.tsx**: 历史记录列表组件

#### 📄 页面层 (src/pages/)
- **popup.tsx**: 弹窗页面逻辑
- **sidepanel.tsx**: 侧边栏页面逻辑
- **floating-window.tsx**: 悬浮窗页面逻辑

## 开发说明

### 添加新功能
1. 在相应的组件文件中添加功能代码
2. 如需新的权限，在 `package.json` 的 `manifest` 部分添加
3. 重新构建和测试

### 样式定制
- 所有样式使用内联方式定义
- 可以修改各组件中的 `style` 对象来调整外观
- 悬浮窗使用高 z-index 确保显示在最前面

### 调试技巧
- 使用浏览器开发者工具调试
- 查看扩展管理页面的错误信息
- 在代码中添加 `console.log` 进行调试

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！